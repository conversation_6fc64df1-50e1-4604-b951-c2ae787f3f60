

using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Pool;

public class MajiangGenerator
{
    private static MajiangGenerator _instance;
    public static MajiangGenerator Inst
    {
        get { return _instance ??= new MajiangGenerator(); }
    }

    // private static MaJiangType[] maJiangTypes = new MaJiangType[] { MaJiangType.Wan, MaJiangType.Tong, MaJiangType.Tiao };
    // private static MaJiangType[] maJiangNormalTypes = new MaJiangType[] { MaJiangType.Wan, MaJiangType.Tong, MaJiangType.Tiao, MaJiangType.Dapai };

    private int[] GetAvailableIds(InfoGate infoGate)
    {
        var allIds = new List<int>(infoGate.majiangIds);
        
        // 随机打乱
        for (int i = 0; i < allIds.Count; i++)
        {
            int randomIndex = Random.Range(i, allIds.Count);
            int temp = allIds[i];
            allIds[i] = allIds[randomIndex];
            allIds[randomIndex] = temp;
        }

        var selectedIds = new int[Mathf.Min(infoGate.majiangIdCount, allIds.Count)];
        for (int i = 0; i < selectedIds.Length; i++)
        {
            selectedIds[i] = allIds[i];
        }

        return selectedIds;
    }
    internal List<MaJiangVo> Generate(InfoGate infoGate)
    {

#if UNITY_EDITOR
        // return GetTestMajiangs(8);
#endif

        int chiCount = infoGate.chiCount;
        chiCount = 0;
        int pengCount = infoGate.majiangCount / 2 - chiCount;
        int topPengCount = infoGate.topCount / 2;

        var majiangs = new List<MaJiangVo>();
        // GenerateChiGroups(majiangs, chiCount, infoGate);
        GeneratePengGroups(majiangs, pengCount - topPengCount, infoGate);
        // GenerateTopLayerPengGroups(majiangs, infoGate.topTypeCount, topPengCount, infoGate);
        ShuffleMajiangs(majiangs);
        return majiangs;
    }

    private void ShuffleMajiangs(List<MaJiangVo> majiangs)
    {
        for (int i = 0; i < majiangs.Count; i++)
        {
            var temp = majiangs[i];
            int randomIndex = Random.Range(i, majiangs.Count);
            majiangs[i] = majiangs[randomIndex];
            majiangs[randomIndex] = temp;
        }
    }
    // private void GenerateChiGroups(List<MaJiangVo> majiangs, int chiCount, InfoGate infoGate = null)
    // {
    //     var availableNums = GetAvailableIds(infoGate);
    //     var numTypeCount = maJiangTypes.Length;
    //     for (int i = 0; i < chiCount; i++)
    //     {
    //         var type = maJiangTypes[Random.Range(0, numTypeCount)];
    //         var num = availableNums[Random.Range(0, availableNums.Length)];

    //         // 确保num+2不超过9（万筒条的最大值）
    //         if (num > 7) num = Random.Range(1, 8);

    //         var m1 = new MaJiangVo(type, num, num + 1);
    //         var m2 = new MaJiangVo(type, num + 2, 0);
    //         // m1.Brother = m2;
    //         // m2.Brother = m1;
    //         majiangs.Add(m1);
    //         majiangs.Add(m2);
    //     }
    // }
    private void GeneratePengGroups(List<MaJiangVo> majiangs, int pengCount, InfoGate infoGate = null)
    {
        var availableIds = GetAvailableIds(infoGate);
        var availableCount = availableIds.Length;
        var index = 0;
        for (int i = 0; i < pengCount; i++)
        {
            var curIdx = index % availableCount;
            var id = availableIds[curIdx];
            var infoMajiang = ConfigMajiang.GetData(id);
            var m1 = new MaJiangVo((MaJiangType)infoMajiang.type, infoMajiang.num, infoMajiang.num);
            var m2 = new MaJiangVo((MaJiangType)infoMajiang.type, infoMajiang.num, 0);
            majiangs.Add(m1);
            majiangs.Add(m2);
            index++;
        }
    }

    // private void GenerateTopLayerPengGroups(List<MaJiangVo> majiangs, int typeCount, int topPentCount, InfoGate infoGate = null)
    // {
    //     if (typeCount == 0 || topPentCount == 0)
    //         return;

    //     var availableNums = GetAvailableIds(infoGate);
    //     var numTypeCount = maJiangNormalTypes.Length;
    //     var typeInfos = new List<(MaJiangType, int)>();
    //     for (int i = 0; i < typeCount; i++)
    //     {
    //         var type = maJiangNormalTypes[Random.Range(0, numTypeCount)];
    //         int num;

    //         if (type == MaJiangType.Dapai)
    //         {
    //             // 大牌使用1-15的范围
    //             num = Random.Range(1, 16);
    //         }
    //         else
    //         {
    //             // 万筒条使用配置的可用数字
    //             num = availableNums[Random.Range(0, availableNums.Length)];
    //         }

    //         typeInfos.Add((type, num));
    //     }

    //     for (int i = 0; i < topPentCount; i++)
    //     {
    //         var typeInfo = typeInfos[i % typeCount];
    //         var type = typeInfo.Item1;
    //         int num = typeInfo.Item2;

    //         // Log.Debug("type:" + type + " num:" + num);
    //         var m1 = new MaJiangVo(type, num, num);
    //         var m2 = new MaJiangVo(type, num, 0);
    //         // m1.Brother = m2;
    //         // m2.Brother = m1;
    //         majiangs.Add(m1);
    //         majiangs.Add(m2);
    //     }
    // }



    internal List<MaJiangVo> GetTestMajiangs(int level)
    {
        var majiangVos = ListPool<MaJiangVo>.Get();
        MaJiangVo m1, m2;
        switch (level)
        {
            case 2:
                //无解的麻将尝试相互匹配
                m1 = new MaJiangVo(MaJiangType.Wan, 2, 0);
                m2 = new MaJiangVo(MaJiangType.Wan, 3, 4);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);

                m1 = new MaJiangVo(MaJiangType.Wan, 2, 0);
                m2 = new MaJiangVo(MaJiangType.Wan, 3, 4);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);
                break;
            case 3:
                //变换 碰
                m1 = new MaJiangVo(MaJiangType.Wan, 2, 0);
                m2 = new MaJiangVo(MaJiangType.Wan, 3, 4);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);

                m1 = new MaJiangVo(MaJiangType.Wan, 5, 0);
                m2 = new MaJiangVo(MaJiangType.Wan, 5, 5);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);
                break;
            case 4:
                //变换 杠
                m1 = new MaJiangVo(MaJiangType.Wan, 5, 0);
                m2 = new MaJiangVo(MaJiangType.Wan, 3, 4);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);

                m1 = new MaJiangVo(MaJiangType.Wan, 5, 5);
                m2 = new MaJiangVo(MaJiangType.Wan, 5, 5);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);
                break;
            case 5:
                //变换 吃
                m1 = new MaJiangVo(MaJiangType.Wan, 2, 0);
                m2 = new MaJiangVo(MaJiangType.Wan, 3, 4);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);

                m1 = new MaJiangVo(MaJiangType.Wan, 5, 0);
                m2 = new MaJiangVo(MaJiangType.Wan, 6, 7);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);

                m1 = new MaJiangVo(MaJiangType.Wan, 6, 0);
                m2 = new MaJiangVo(MaJiangType.Wan, 6, 6);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);
                break;
            case 6:
                //牌在slotbar上，先走2，5，再走6，66
                m1 = new MaJiangVo(MaJiangType.Wan, 2, 0);
                m2 = new MaJiangVo(MaJiangType.Wan, 3, 4);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);

                m1 = new MaJiangVo(MaJiangType.Wan, 5, 0);
                m2 = new MaJiangVo(MaJiangType.Wan, 6, 7);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);

                m1 = new MaJiangVo(MaJiangType.Wan, 6, 0);
                m2 = new MaJiangVo(MaJiangType.Wan, 6, 6);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);
                break;
            case 7:
                //两张单牌
                m1 = new MaJiangVo(MaJiangType.Wan, 9, 9);
                m2 = new MaJiangVo(MaJiangType.Wan, 9, 0);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);

                m1 = new MaJiangVo(MaJiangType.Wan, 9, 9);
                m2 = new MaJiangVo(MaJiangType.Wan, 9, 0);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);
                break;
            case 8:
                //无解  
                //3,6,4-5,3-3
                //3,6,3-3,4-5(3-3点完立即4-5)
                //3-3,4-5,
                m1 = new MaJiangVo(MaJiangType.Wan, 4, 5);
                m2 = new MaJiangVo(MaJiangType.Wan, 6);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);
                m1 = new MaJiangVo(MaJiangType.Wan, 3, 3);
                m2 = new MaJiangVo(MaJiangType.Wan, 3);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);
                break;
            case 9:
                m1 = new MaJiangVo(MaJiangType.Wan, 4, 5);
                m2 = new MaJiangVo(MaJiangType.Wan, 6);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);
                m1 = new MaJiangVo(MaJiangType.Wan, 3, 3);
                m2 = new MaJiangVo(MaJiangType.Wan, 3);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);
                m1 = new MaJiangVo(MaJiangType.Wan, 3, 3);
                m2 = new MaJiangVo(MaJiangType.Wan, 3);
                // m1.Brother = m2;
                // m2.Brother = m1;
                majiangVos.Add(m1);
                majiangVos.Add(m2);
                // m1 = new MaJiangVo(MaJiangType.Wan, 4, 5);
                // m2 = new MaJiangVo(MaJiangType.Wan, 6);
                // m1.Brother = m2;
                // m2.Brother = m1;
                // majiangVos.Add(m1);
                // majiangVos.Add(m2);
                // m1 = new MaJiangVo(MaJiangType.Wan, 3, 3);
                // m2 = new MaJiangVo(MaJiangType.Wan, 3);
                // m1.Brother = m2;
                // m2.Brother = m1;
                // majiangVos.Add(m1);
                // majiangVos.Add(m2);
                break;
        }
        return majiangVos;
    }
}