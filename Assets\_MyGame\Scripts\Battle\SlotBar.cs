using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using DG.Tweening;
using FairyGUI;
using UnityEngine;

public class SlotBar : MonoBehaviour
{
    public Action<bool> OnGameDone;
    [NonSerialized] public int useSlotCount = 8;
    public Slot[] slots;
    private bool isGameOver;
    private List<MaJiangGroup> slotMajiangVos;

    internal void Init(int lockCount)
    {
        slotMajiangVos = MajiangManager.Inst.slotMajiangVos;
        ClearSlot();

        var count = slots.Length;

        var videoCount = 0;
        for (int i = count - 1; i >= 0; i--)
        {
            var slot = slots[i];
            var isLock = i >= count - 1 - lockCount && i != count - 1;//最后一位是辅助位
            InitSlotLock(slot, isLock);
        }

        if (videoCount > 0)
        {
            GGISdk.Inst.ReportVideoExposure(videoCount);
        }

        UpdateHelpSlotPos();
    }
    public Slot GetFirstLockSlot()
    {
        for (int i = 0; i < slots.Length - 1; i++)
        {
            var slot = slots[i];
            if (slot.IsLock)
            {
                return slot;
            }
        }
        return null;
    }
    private void UpdateHelpSlotPos()
    {
        useSlotCount = 0;
        var slotCount = slots.Length;
        var helpSlot = slots[slotCount - 1];
        var isSetHelpSlot = false;
        for (int i = 0; i < slotCount - 1; i++)
        {
            var slot = slots[i];
            if (!slot.IsLock)
            {
                useSlotCount++;
            }
            if (!isSetHelpSlot && slot.IsLock)
            {
                var preSlot = slots[i - 1];
                isSetHelpSlot = true;
                helpSlot.transform.position = preSlot.transform.position;
            }
        }

        if (!isSetHelpSlot)
        {
            helpSlot.transform.position = slots[slotCount - 2].transform.position;
        }
    }

    internal void UnLockSlot()
    {
        if (this == null)
            return;
        for (int i = 0; i < slots.Length - 1; i++)
        {
            var slot = slots[i];
            if (slot.IsLock)
            {
                ChangeSlotLock(slot, false);
                UpdateHelpSlotPos();
                break;
            }
        }
    }

    internal void InitSlotLock(Slot slot, bool isLock)
    {
        var lockGo = slot.transform.Find("lock");
        if (lockGo != null)
        {
            if (isLock)
            {
                var spriteUrl = "Textures/lockSlot" + LanguageManager.GetLanguageSuffix(LanguageManager.CurrentLanguage);
                var spriteRender = lockGo.GetComponent<SpriteRenderer>();
                AssetBundleManager.LoadSprite(spriteUrl,
                (sprite) =>
                {
                    if (this == null) return;
                    spriteRender.sprite = sprite;
                });
            }
            
            lockGo.gameObject.SetActive(isLock);
        }
        slot.IsLock = isLock;
    }

    internal void ChangeSlotLock(Slot slot, bool isLock)
    {
        var lockGo = slot.transform.Find("lock");
        if (lockGo != null)
        {
            lockGo.gameObject.SetActive(isLock);
        }
        slot.IsLock = isLock;
    }

    internal void Add(MaJiangGroup maJiang)
    {
        if (!CanAddMajiang(maJiang))
            return;

        MajiangManager.Inst.RemoveMajiang(maJiang.Id);
        InsertMajiang2Slot(maJiang);
        MergeMajiang();
        UpdateSlotView();
        UpdateMajiangVo();

        Timers.inst.Add(0.5f, 1, (obj) =>
        {
            CheckWin();
        });
    }

    private bool CanAddMajiang(MaJiangGroup maJiang)
    {
        if (isGameOver || maJiang == null)
            return false;

        if (slotMajiangVos.Count + maJiang.Space > useSlotCount)
        {
            TipMgr.ShowTip(LangUtil.GetText("txtNoSlot"));
            return false;
        }
        return true;
    }

    private void InsertMajiang2Slot(MaJiangGroup addMajiang)
    {
        var insertPos = 0;
        for (int i = 0; i < slotMajiangVos.Count; i++)
        {
            var majiang = slotMajiangVos[i];
            insertPos = majiang.CanLink(addMajiang);
            if (insertPos != 0)
            {
                var insertIndex = insertPos == -1 ? i : i + majiang.Space;
                MoveMajiang2Slot(addMajiang, insertIndex);
                MoveMajiang2Right(insertIndex, addMajiang.Space);
                for (int j = 0; j < addMajiang.Space; j++)
                {
                    slotMajiangVos.Insert(insertIndex, addMajiang);
                }
                break;
            }
            i += majiang.Space - 1;
        }
        if (insertPos == 0)
        {
            MoveMajiang2Slot(addMajiang, slotMajiangVos.Count);
            for (int i = 0; i < addMajiang.Space; i++)
            {
                slotMajiangVos.Add(addMajiang);
            }
        }
    }

    private void MergeMajiang()
    {
        for (int i = 0; i < slotMajiangVos.Count; i++)
        {
            var srcMajiang = slotMajiangVos[i];
            for (int j = i + srcMajiang.Space; j < slotMajiangVos.Count; j++)
            {
                var targetMajiang = slotMajiangVos[j];
                if (srcMajiang.CanLink(targetMajiang) != 0)
                {
                    var moveSpace = targetMajiang.Space + srcMajiang.Space;
                    PlayMerge(srcMajiang, targetMajiang);
                    MoveMajiang2Left(j + targetMajiang.Space, moveSpace);
                    for (int delTarget = 0; delTarget < targetMajiang.Space; delTarget++)
                    {
                        slotMajiangVos.RemoveAt(j);
                    }
                    for (int delSrc = 0; delSrc < srcMajiang.Space; delSrc++)
                    {
                        slotMajiangVos.RemoveAt(i);
                    }
                    return;
                }
                j += targetMajiang.Space - 1;
            }
            i += srcMajiang.Space - 1;
        }
    }

    private void MoveMajiang2Slot(MaJiangGroup majiang, int insterIndex)
    {
        majiang.AddAction(new ActionMove2Slot(majiang, slots, insterIndex));
    }

    private void MoveMajiang2Right(int insterIndex, int moveSpace)
    {
        var moveIndex = 0;
        for (int i = useSlotCount - 1; i >= insterIndex; i--)
        {
            var majiang = GetSlotMajiang(i);
            if (majiang == null)
                continue;

            if (majiang.Space > 1)
            {
                //连体麻将只控制第一个
                i -= majiang.Space - 1;
            }
            majiang.AddAction(new ActionMoveSlotMajiang(majiang, slots, i + moveSpace, moveIndex));
            moveIndex++;
        }
    }

    private void PlayMerge(MaJiangGroup srcMajiang, MaJiangGroup targetMajiang)
    {
        //合并动作添加到最后完成动作的麻将
        if (srcMajiang.GetLeftActionCount() > targetMajiang.GetLeftActionCount())
        {
            srcMajiang.AddAction(new ActionMerge(srcMajiang, targetMajiang));
        }
        else if (srcMajiang.GetLeftActionCount() < targetMajiang.GetLeftActionCount())
        {
            targetMajiang.AddAction(new ActionMerge(srcMajiang, targetMajiang));
        }
        else
        {
            if (srcMajiang.ClickTime > targetMajiang.ClickTime)
            {
                srcMajiang.AddAction(new ActionMerge(srcMajiang, targetMajiang));
            }
            else
            {
                targetMajiang.AddAction(new ActionMerge(srcMajiang, targetMajiang));
            }
        }
    }

    private void MoveMajiang2Left(int startMoveIndex, int moveSpace)
    {
        var moveIndex = 0;
        for (int i = startMoveIndex; i < useSlotCount; i++)
        {
            var majiang = GetSlotMajiang(i);
            if (majiang == null)
                return;

            majiang.AddAction(new ActionWait(0.5f));
            majiang.AddAction(new ActionMoveSlotMajiang(majiang, slots, i - moveSpace, moveIndex));
            moveIndex++;

            //连体麻将只控制第一个
            i += majiang.Space - 1;
        }
    }

    private void UpdateMajiangVo()
    {
        MajiangManager.Inst.UpdateMajiangToMatch();
    }

    internal MaJiangGroup GetSlotMajiang(int index)
    {
        if (index < 0 || index >= slotMajiangVos.Count)
            return null;
        return slotMajiangVos[index];
    }

    internal void ClearSlot()
    {
        slotMajiangVos.Clear();
    }

    internal bool IsSlotClear
    {
        get
        {
            return slotMajiangVos.Count == 0;
        }
    }
    internal bool IsSlotFull
    {
        get
        {
            return slotMajiangVos.Count == useSlotCount;
        }
    }
    private void UpdateSlotView()
    {
#if UNITY_EDITOR
        for (int i = 0; i < slots.Length; i++)
        {
            var slot = slots[i];
            var majiang = GetSlotMajiang(i);
            if (majiang != null)
            {
                slot.SetText(majiang.MajiangVo.num1 + "," + majiang.MajiangVo.num2);
            }
            else
            {
                slot.SetText(string.Empty);
            }
        }
#endif
    }

    public void CheckWin()
    {
        if (IsSlotFull)
        {
            GameOver();
        }
        else if (BattleScene.Inst.MajinagIsClear && IsSlotClear)
        {
            GameWin();
        }
    }

    private void GameWin()
    {
        if (isGameOver)
            return;
        isGameOver = true;
        OnGameDone?.Invoke(true);
    }

    private void GameOver()
    {
        if (isGameOver)
            return;
        isGameOver = true;
        OnGameDone?.Invoke(false);
    }

    internal MaJiangVo GetNeedMatchMajiangVo()
    {
        var emptyCount = useSlotCount - slotMajiangVos.Count;

        MaJiangVo matchMajiangVo = null;
        for (int i = 0; i < useSlotCount; i++)
        {
            var majiang = GetSlotMajiang(i);
            if (majiang == null)
                continue;

            var needSpace = 3 - majiang.Space;
            if (needSpace <= emptyCount)
            {
                matchMajiangVo = majiang.MajiangVo;
                break;
            }
        }

        if (emptyCount < useSlotCount && matchMajiangVo == null)
        {
            TipMgr.ShowTip(LangUtil.GetText("txtNoSlot"));
        }

        return matchMajiangVo;
    }

    internal List<MaJiangVo> GetValidMatchMajiangVos()
    {
        var emptyCount = useSlotCount - slotMajiangVos.Count;
        var validMajiangVos = new List<MaJiangVo>();

        for (int i = 0; i < useSlotCount; i++)
        {
            var majiang = GetSlotMajiang(i);
            if (majiang == null)
                continue;

            var needSpace = 3 - majiang.Space;
            if (needSpace <= emptyCount)
            {
                validMajiangVos.Add(majiang.MajiangVo);
            }
        }

        return validMajiangVos;
    }

    internal int GetEmptySlotCount()
    {
        return useSlotCount - slotMajiangVos.Count;
    }

    internal void Clear2Table()
    {
        isGameOver = false;
        for (int i = 0; i < useSlotCount; i++)
        {
            var majiang = GetSlotMajiang(i);
            if (majiang == null)
                continue;

            MajiangManager.Inst.AddMajiang(majiang);

            var areaPos = BattleScene.Inst.bornArea.transform.position;
            var targetPos = BattleScene.Inst.bornArea.GetRandonPos();
            targetPos.y = areaPos.y;
            majiang.FlyToTable(targetPos);
        }
        ClearSlot();
        UpdateSlotView();
    }
}